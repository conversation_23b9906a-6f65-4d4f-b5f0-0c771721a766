import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { gradingService } from '@/services';
import type {
  GradeDto,
  SubjectDto,
  PaperDto,
  GradeBoundaryDto,
  GradeConfigurationDto,
  CreateGradeRequest,
  UpdateGradeRequest,
  CreateSubjectRequest,
  UpdateSubjectRequest,
  CreatePaperRequest,
  UpdatePaperRequest,
  CreateGradeBoundaryRequest,
  UpdateGradeBoundaryRequest,
  CreateGradeConfigurationRequest,
  UpdateGradeConfigurationRequest,
  GradeLevel
} from '@/interfaces';

export const useGradingStore = defineStore('grading', () => {
  // State
  const grades = ref<GradeDto[]>([]);
  const subjects = ref<SubjectDto[]>([]);
  const papers = ref<PaperDto[]>([]);
  const gradeBoundaries = ref<GradeBoundaryDto[]>([]);
  const gradeConfigurations = ref<GradeConfigurationDto[]>([]);
  const currentGrade = ref<GradeDto | null>(null);
  const currentSubject = ref<SubjectDto | null>(null);
  const currentPaper = ref<PaperDto | null>(null);
  const currentGradeBoundary = ref<GradeBoundaryDto | null>(null);
  const currentGradeConfiguration = ref<GradeConfigurationDto | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  
  // Filters and pagination
  const searchQuery = ref('');
  const yearFilter = ref<number | 'All'>('All');
  const subjectFilter = ref<string | 'All'>('All');
  const paperFilter = ref<string | 'All'>('All');
  const gradeFilter = ref<GradeLevel | 'All'>('All');
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // Getters
  const filteredGrades = computed(() => {
    let filtered = grades.value;

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(grade => 
        grade.studentId?.toLowerCase().includes(query) ||
        grade.assignedGrade?.toLowerCase().includes(query)
      );
    }

    if (yearFilter.value !== 'All') {
      filtered = filtered.filter(grade => grade.year === yearFilter.value);
    }

    if (subjectFilter.value !== 'All') {
      filtered = filtered.filter(grade => grade.subjectId === subjectFilter.value);
    }

    if (paperFilter.value !== 'All') {
      filtered = filtered.filter(grade => grade.paperId === paperFilter.value);
    }

    if (gradeFilter.value !== 'All') {
      filtered = filtered.filter(grade => grade.assignedGrade === gradeFilter.value);
    }

    return filtered;
  });

  const filteredSubjects = computed(() => {
    let filtered = subjects.value;

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(subject => 
        subject.name?.toLowerCase().includes(query) ||
        subject.code?.toLowerCase().includes(query)
      );
    }

    return filtered.filter(subject => !subject.isDeleted);
  });

  const activeSubjects = computed(() =>
    subjects.value.filter(subject => subject.isActive && !subject.isDeleted)
  );

  const activePapers = computed(() =>
    papers.value.filter(paper => paper.isActive && !paper.isDeleted)
  );

  const papersBySubject = computed(() => (subjectId: string) =>
    papers.value.filter(paper => paper.subjectId === subjectId && paper.isActive && !paper.isDeleted)
  );

  const activeGradeConfigurations = computed(() =>
    gradeConfigurations.value.filter(config => config.isActive && !config.isDeleted)
  );

  // Actions
  const clearError = () => {
    error.value = null;
  };

  // Initialize store with mock data
  const initializeStore = async () => {
    try {
      await Promise.all([
        fetchGrades(),
        fetchSubjects(),
        fetchPapers(),
        fetchGradeBoundaries()
      ]);
    } catch (err) {
      console.error('Failed to initialize grading store:', err);
    }
  };

  // Grade Actions
  const fetchGrades = async () => {
    isLoading.value = true;
    error.value = null;
    try {
      grades.value = await gradingService.getAllGrades();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch grades';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchGradeById = async (id: string) => {
    isLoading.value = true;
    error.value = null;
    try {
      currentGrade.value = await gradingService.getGradeById(id);
      return currentGrade.value;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch grade';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createGrade = async (gradeData: CreateGradeRequest) => {
    isLoading.value = true;
    error.value = null;
    try {
      const newGrade = await gradingService.createGrade(gradeData);
      grades.value.push(newGrade);
      return newGrade;
    } catch (err: any) {
      error.value = err.message || 'Failed to create grade';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateGrade = async (id: string, gradeData: UpdateGradeRequest) => {
    isLoading.value = true;
    error.value = null;
    try {
      const updatedGrade = await gradingService.updateGrade(id, gradeData);
      const index = grades.value.findIndex(g => g.id === id);
      if (index !== -1) {
        grades.value[index] = updatedGrade;
      }
      return updatedGrade;
    } catch (err: any) {
      error.value = err.message || 'Failed to update grade';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteGrade = async (id: string) => {
    isLoading.value = true;
    error.value = null;
    try {
      await gradingService.deleteGrade(id);
      grades.value = grades.value.filter(g => g.id !== id);
    } catch (err: any) {
      error.value = err.message || 'Failed to delete grade';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // Subject Actions
  const fetchSubjects = async () => {
    isLoading.value = true;
    error.value = null;
    try {
      subjects.value = await gradingService.getAllSubjects();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch subjects';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createSubject = async (subjectData: CreateSubjectRequest) => {
    isLoading.value = true;
    error.value = null;
    try {
      const newSubject = await gradingService.createSubject(subjectData);
      subjects.value.push(newSubject);
      return newSubject;
    } catch (err: any) {
      error.value = err.message || 'Failed to create subject';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateSubject = async (id: string, subjectData: UpdateSubjectRequest) => {
    isLoading.value = true;
    error.value = null;
    try {
      const updatedSubject = await gradingService.updateSubject(id, subjectData);
      const index = subjects.value.findIndex(s => s.id === id);
      if (index !== -1) {
        subjects.value[index] = updatedSubject;
      }
      return updatedSubject;
    } catch (err: any) {
      error.value = err.message || 'Failed to update subject';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteSubject = async (id: string) => {
    isLoading.value = true;
    error.value = null;
    try {
      await gradingService.deleteSubject(id);
      subjects.value = subjects.value.filter(s => s.id !== id);
    } catch (err: any) {
      error.value = err.message || 'Failed to delete subject';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // Paper Actions
  const fetchPapers = async () => {
    isLoading.value = true;
    error.value = null;
    try {
      papers.value = await gradingService.getAllPapers();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch papers';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchPapersBySubject = async (subjectId: string) => {
    isLoading.value = true;
    error.value = null;
    try {
      const subjectPapers = await gradingService.getPapersBySubject(subjectId);
      return subjectPapers;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch papers for subject';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createPaper = async (paperData: CreatePaperRequest) => {
    isLoading.value = true;
    error.value = null;
    try {
      const newPaper = await gradingService.createPaper(paperData);
      papers.value.push(newPaper);
      return newPaper;
    } catch (err: any) {
      error.value = err.message || 'Failed to create paper';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updatePaper = async (id: string, paperData: UpdatePaperRequest) => {
    isLoading.value = true;
    error.value = null;
    try {
      const updatedPaper = await gradingService.updatePaper(id, paperData);
      const index = papers.value.findIndex(p => p.id === id);
      if (index !== -1) {
        papers.value[index] = updatedPaper;
      }
      return updatedPaper;
    } catch (err: any) {
      error.value = err.message || 'Failed to update paper';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deletePaper = async (id: string) => {
    isLoading.value = true;
    error.value = null;
    try {
      await gradingService.deletePaper(id);
      papers.value = papers.value.filter(p => p.id !== id);
    } catch (err: any) {
      error.value = err.message || 'Failed to delete paper';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // Grade Boundary Actions
  const fetchGradeBoundaries = async () => {
    isLoading.value = true;
    error.value = null;
    try {
      gradeBoundaries.value = await gradingService.getAllGradeBoundaries();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch grade boundaries';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createGradeBoundary = async (boundaryData: CreateGradeBoundaryRequest) => {
    isLoading.value = true;
    error.value = null;
    try {
      const newBoundary = await gradingService.createGradeBoundary(boundaryData);
      gradeBoundaries.value.push(newBoundary);
      return newBoundary;
    } catch (err: any) {
      error.value = err.message || 'Failed to create grade boundary';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateGradeBoundary = async (id: string, boundaryData: UpdateGradeBoundaryRequest) => {
    isLoading.value = true;
    error.value = null;
    try {
      const updatedBoundary = await gradingService.updateGradeBoundary(id, boundaryData);
      const index = gradeBoundaries.value.findIndex(b => b.id === id);
      if (index !== -1) {
        gradeBoundaries.value[index] = updatedBoundary;
      }
      return updatedBoundary;
    } catch (err: any) {
      error.value = err.message || 'Failed to update grade boundary';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteGradeBoundary = async (id: string) => {
    isLoading.value = true;
    error.value = null;
    try {
      await gradingService.deleteGradeBoundary(id);
      gradeBoundaries.value = gradeBoundaries.value.filter(b => b.id !== id);
    } catch (err: any) {
      error.value = err.message || 'Failed to delete grade boundary';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  return {
    // State
    grades,
    subjects,
    papers,
    gradeBoundaries,
    gradeConfigurations,
    currentGrade,
    currentSubject,
    currentPaper,
    currentGradeBoundary,
    currentGradeConfiguration,
    isLoading,
    error,
    searchQuery,
    yearFilter,
    subjectFilter,
    paperFilter,
    gradeFilter,
    pagination,

    // Getters
    filteredGrades,
    filteredSubjects,
    activeSubjects,
    activePapers,
    papersBySubject,
    activeGradeConfigurations,

    // Actions
    clearError,
    initializeStore,
    fetchGrades,
    fetchGradeById,
    createGrade,
    updateGrade,
    deleteGrade,
    fetchSubjects,
    createSubject,
    updateSubject,
    deleteSubject,
    fetchPapers,
    fetchPapersBySubject,
    createPaper,
    updatePaper,
    deletePaper,
    fetchGradeBoundaries,
    createGradeBoundary,
    updateGradeBoundary,
    deleteGradeBoundary,
  };
});
