import apiClient from './api-client';
import type {
  GradeDto,
  SubjectDto,
  PaperDto,
  GradeBoundaryDto,
  GradeConfigurationDto,
  CreateGradeRequest,
  UpdateGradeRequest,
  CreateSubjectRequest,
  UpdateSubjectRequest,
  CreatePaperRequest,
  UpdatePaperRequest,
  CreateGradeBoundaryRequest,
  UpdateGradeBoundaryRequest,
  CreateGradeConfigurationRequest,
  UpdateGradeConfigurationRequest,
  ExamType,
  ComprehensiveGradeBoundaryRequest,
  BulkGradeBoundaryResponse
} from '@/interfaces';

// Exam Type Constants
export const EXAM_TYPES: { value: ExamType; label: string; description: string }[] = [
  { value: 'MSCE', label: 'MSCE', description: 'Malawi School Certificate of Education' },
  { value: 'PLCE', label: 'PLCE', description: 'Primary Leaving Certificate of Education' },
  { value: 'JCE', label: 'JCE', description: 'Junior Certificate of Education' },
  { value: 'PSLCE', label: 'PSLCE', description: 'Primary School Leaving Certificate of Education' },
  { value: 'TTC', label: 'TTC', description: 'Teacher Training Certificate' }
];

// Mock data that matches the score entry system
const mockSubjects: SubjectDto[] = [
  {
    id: 'SUBJ001',
    name: 'Mathematics',
    code: 'MATH',
    description: 'Pure and Applied Mathematics including Algebra, Geometry, Calculus, and Statistics',
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'SUBJ002',
    name: 'English Language',
    code: 'ENG',
    description: 'English Language skills including Grammar, Composition, Literature, and Communication',
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'SUBJ003',
    name: 'Physical Science',
    code: 'PHYS',
    description: 'Combined Physics and Chemistry covering fundamental scientific principles',
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'SUBJ004',
    name: 'Biology',
    code: 'BIO',
    description: 'Life Sciences including Botany, Zoology, Ecology, and Human Biology',
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'SUBJ005',
    name: 'Chemistry',
    code: 'CHEM',
    description: 'Chemical principles, reactions, and laboratory techniques',
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'SUBJ006',
    name: 'Physics',
    code: 'PHYS',
    description: 'Physical principles, mechanics, electricity, and modern physics',
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'SUBJ007',
    name: 'Geography',
    code: 'GEO',
    description: 'Physical and Human Geography including climate, landforms, and population studies',
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'SUBJ008',
    name: 'History',
    code: 'HIST',
    description: 'World and African History covering major historical events and civilizations',
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'SUBJ009',
    name: 'Economics',
    code: 'ECON',
    description: 'Microeconomics and Macroeconomics principles and applications',
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  }
];

// Paper data that matches the score entry system
const mockPapers: PaperDto[] = [
  // Mathematics Papers
  {
    id: 'PAP001',
    subjectId: 'SUBJ001',
    name: 'Paper 1 (Pure Mathematics)',
    code: 'MATH-P1',
    description: 'Pure Mathematics including Algebra, Geometry, and Calculus',
    paperNumber: 1,
    duration: 180, // 3 hours
    maxMarks: 100,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'PAP002',
    subjectId: 'SUBJ001',
    name: 'Paper 2 (Applied Mathematics)',
    code: 'MATH-P2',
    description: 'Applied Mathematics including Statistics and Mechanics',
    paperNumber: 2,
    duration: 180, // 3 hours
    maxMarks: 100,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },

  // English Language Papers
  {
    id: 'PAP003',
    subjectId: 'SUBJ002',
    name: 'Paper 1 (Language)',
    code: 'ENG-P1',
    description: 'English Language skills including Grammar and Composition',
    paperNumber: 1,
    duration: 150, // 2.5 hours
    maxMarks: 100,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'PAP004',
    subjectId: 'SUBJ002',
    name: 'Paper 2 (Literature)',
    code: 'ENG-P2',
    description: 'English Literature analysis and interpretation',
    paperNumber: 2,
    duration: 150, // 2.5 hours
    maxMarks: 100,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },

  // Physical Science Papers
  {
    id: 'PAP005',
    subjectId: 'SUBJ003',
    name: 'Paper 1 (Theory)',
    code: 'PHYS-P1',
    description: 'Physical Science theoretical concepts and principles',
    paperNumber: 1,
    duration: 180, // 3 hours
    maxMarks: 100,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'PAP006',
    subjectId: 'SUBJ003',
    name: 'Paper 2 (Practical)',
    code: 'PHYS-P2',
    description: 'Physical Science practical experiments and applications',
    paperNumber: 2,
    duration: 120, // 2 hours
    maxMarks: 50,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'PAP007',
    subjectId: 'SUBJ003',
    name: 'Paper 3 (Alternative to Practical)',
    code: 'PHYS-P3',
    description: 'Alternative to practical for centers without laboratory facilities',
    paperNumber: 3,
    duration: 120, // 2 hours
    maxMarks: 50,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },

  // Biology Papers
  {
    id: 'PAP008',
    subjectId: 'SUBJ004',
    name: 'Paper 1 (Theory)',
    code: 'BIO-P1',
    description: 'Biology theoretical concepts covering all life sciences',
    paperNumber: 1,
    duration: 180, // 3 hours
    maxMarks: 100,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'PAP009',
    subjectId: 'SUBJ004',
    name: 'Paper 2 (Practical)',
    code: 'BIO-P2',
    description: 'Biology practical experiments and field work',
    paperNumber: 2,
    duration: 120, // 2 hours
    maxMarks: 50,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },

  // Chemistry Papers
  {
    id: 'PAP010',
    subjectId: 'SUBJ005',
    name: 'Paper 1 (Theory)',
    code: 'CHEM-P1',
    description: 'Chemistry theoretical concepts and principles',
    paperNumber: 1,
    duration: 180, // 3 hours
    maxMarks: 100,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'PAP011',
    subjectId: 'SUBJ005',
    name: 'Paper 2 (Practical)',
    code: 'CHEM-P2',
    description: 'Chemistry practical experiments and laboratory work',
    paperNumber: 2,
    duration: 120, // 2 hours
    maxMarks: 50,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },

  // Physics Papers
  {
    id: 'PAP012',
    subjectId: 'SUBJ006',
    name: 'Paper 1 (Theory)',
    code: 'PHYS-P1',
    description: 'Physics theoretical concepts and principles',
    paperNumber: 1,
    duration: 180, // 3 hours
    maxMarks: 100,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'PAP013',
    subjectId: 'SUBJ006',
    name: 'Paper 2 (Practical)',
    code: 'PHYS-P2',
    description: 'Physics practical experiments and laboratory work',
    paperNumber: 2,
    duration: 120, // 2 hours
    maxMarks: 50,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },

  // Geography Papers
  {
    id: 'PAP014',
    subjectId: 'SUBJ007',
    name: 'Paper 1 (Physical Geography)',
    code: 'GEO-P1',
    description: 'Physical Geography including climate, landforms, and natural processes',
    paperNumber: 1,
    duration: 150, // 2.5 hours
    maxMarks: 100,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'PAP015',
    subjectId: 'SUBJ007',
    name: 'Paper 2 (Human Geography)',
    code: 'GEO-P2',
    description: 'Human Geography including population, settlements, and economic activities',
    paperNumber: 2,
    duration: 150, // 2.5 hours
    maxMarks: 100,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },

  // History Papers
  {
    id: 'PAP016',
    subjectId: 'SUBJ008',
    name: 'Paper 1 (World History)',
    code: 'HIST-P1',
    description: 'World History covering major global historical events',
    paperNumber: 1,
    duration: 150, // 2.5 hours
    maxMarks: 100,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },
  {
    id: 'PAP017',
    subjectId: 'SUBJ008',
    name: 'Paper 2 (African History)',
    code: 'HIST-P2',
    description: 'African History focusing on African civilizations and colonial period',
    paperNumber: 2,
    duration: 150, // 2.5 hours
    maxMarks: 100,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  },

  // Economics Papers
  {
    id: 'PAP018',
    subjectId: 'SUBJ009',
    name: 'Paper 1 (Microeconomics)',
    code: 'ECON-P1',
    description: 'Microeconomics principles and applications',
    paperNumber: 1,
    duration: 150, // 2.5 hours
    maxMarks: 100,
    isActive: true,
    createdBy: 'system',
    dateCreated: new Date('2023-01-01'),
    isDeleted: false
  }
];

// Grade boundaries for each paper and year (consistent with MANEB grading system)
const mockGradeBoundaries: GradeBoundaryDto[] = [
  // Mathematics Paper 1 - 2024 - MSCE
  { id: 'GB001', examType: 'MSCE', subjectId: 'SUBJ001', paperId: 'PAP001', year: 2024, gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB002', examType: 'MSCE', subjectId: 'SUBJ001', paperId: 'PAP001', year: 2024, gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB003', examType: 'MSCE', subjectId: 'SUBJ001', paperId: 'PAP001', year: 2024, gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB004', examType: 'MSCE', subjectId: 'SUBJ001', paperId: 'PAP001', year: 2024, gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB005', examType: 'MSCE', subjectId: 'SUBJ001', paperId: 'PAP001', year: 2024, gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },

  // Mathematics Paper 2 - 2024 - MSCE
  { id: 'GB006', examType: 'MSCE', subjectId: 'SUBJ001', paperId: 'PAP002', year: 2024, gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB007', examType: 'MSCE', subjectId: 'SUBJ001', paperId: 'PAP002', year: 2024, gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB008', examType: 'MSCE', subjectId: 'SUBJ001', paperId: 'PAP002', year: 2024, gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB009', examType: 'MSCE', subjectId: 'SUBJ001', paperId: 'PAP002', year: 2024, gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB010', examType: 'MSCE', subjectId: 'SUBJ001', paperId: 'PAP002', year: 2024, gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },

  // English Language Paper 1 - 2024 - MSCE
  { id: 'GB011', examType: 'MSCE', subjectId: 'SUBJ002', paperId: 'PAP003', year: 2024, gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB012', examType: 'MSCE', subjectId: 'SUBJ002', paperId: 'PAP003', year: 2024, gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB013', examType: 'MSCE', subjectId: 'SUBJ002', paperId: 'PAP003', year: 2024, gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB014', examType: 'MSCE', subjectId: 'SUBJ002', paperId: 'PAP003', year: 2024, gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB015', examType: 'MSCE', subjectId: 'SUBJ002', paperId: 'PAP003', year: 2024, gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },

  // Physical Science - 2024
  { id: 'GB011', subjectId: 'SUBJ003', year: 2024, gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB012', subjectId: 'SUBJ003', year: 2024, gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB013', subjectId: 'SUBJ003', year: 2024, gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB014', subjectId: 'SUBJ003', year: 2024, gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB015', subjectId: 'SUBJ003', year: 2024, gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },

  // Biology - 2024
  { id: 'GB016', subjectId: 'SUBJ004', year: 2024, gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB017', subjectId: 'SUBJ004', year: 2024, gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB018', subjectId: 'SUBJ004', year: 2024, gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB019', subjectId: 'SUBJ004', year: 2024, gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB020', subjectId: 'SUBJ004', year: 2024, gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },

  // Chemistry - 2024
  { id: 'GB021', subjectId: 'SUBJ005', year: 2024, gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB022', subjectId: 'SUBJ005', year: 2024, gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB023', subjectId: 'SUBJ005', year: 2024, gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB024', subjectId: 'SUBJ005', year: 2024, gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB025', subjectId: 'SUBJ005', year: 2024, gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },

  // Physics - 2024
  { id: 'GB026', subjectId: 'SUBJ006', year: 2024, gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB027', subjectId: 'SUBJ006', year: 2024, gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB028', subjectId: 'SUBJ006', year: 2024, gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB029', subjectId: 'SUBJ006', year: 2024, gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB030', subjectId: 'SUBJ006', year: 2024, gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },

  // Geography - 2024
  { id: 'GB031', subjectId: 'SUBJ007', year: 2024, gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB032', subjectId: 'SUBJ007', year: 2024, gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB033', subjectId: 'SUBJ007', year: 2024, gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB034', subjectId: 'SUBJ007', year: 2024, gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },
  { id: 'GB035', subjectId: 'SUBJ007', year: 2024, gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail', isActive: true, createdBy: 'system', dateCreated: new Date('2024-01-01'), isDeleted: false },

  // Mathematics - 2023 (Historical data)
  { id: 'GB036', subjectId: 'SUBJ001', year: 2023, gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB037', subjectId: 'SUBJ001', year: 2023, gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB038', subjectId: 'SUBJ001', year: 2023, gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB039', subjectId: 'SUBJ001', year: 2023, gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB040', subjectId: 'SUBJ001', year: 2023, gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },

  // English Language - 2023
  { id: 'GB041', subjectId: 'SUBJ002', year: 2023, gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB042', subjectId: 'SUBJ002', year: 2023, gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB043', subjectId: 'SUBJ002', year: 2023, gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB044', subjectId: 'SUBJ002', year: 2023, gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB045', subjectId: 'SUBJ002', year: 2023, gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },

  // Physical Science - 2023
  { id: 'GB051', subjectId: 'SUBJ003', year: 2023, gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB052', subjectId: 'SUBJ003', year: 2023, gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB053', subjectId: 'SUBJ003', year: 2023, gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB054', subjectId: 'SUBJ003', year: 2023, gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB055', subjectId: 'SUBJ003', year: 2023, gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },

  // Biology - 2023
  { id: 'GB056', subjectId: 'SUBJ004', year: 2023, gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB057', subjectId: 'SUBJ004', year: 2023, gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB058', subjectId: 'SUBJ004', year: 2023, gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB059', subjectId: 'SUBJ004', year: 2023, gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },
  { id: 'GB060', subjectId: 'SUBJ004', year: 2023, gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail', isActive: false, createdBy: 'system', dateCreated: new Date('2023-01-01'), isDeleted: false },

  // Mathematics - 2025 (Future planning)
  { id: 'GB046', subjectId: 'SUBJ001', year: 2025, gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction', isActive: true, createdBy: 'system', dateCreated: new Date('2024-12-01'), isDeleted: false },
  { id: 'GB047', subjectId: 'SUBJ001', year: 2025, gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit', isActive: true, createdBy: 'system', dateCreated: new Date('2024-12-01'), isDeleted: false },
  { id: 'GB048', subjectId: 'SUBJ001', year: 2025, gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-12-01'), isDeleted: false },
  { id: 'GB049', subjectId: 'SUBJ001', year: 2025, gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass', isActive: true, createdBy: 'system', dateCreated: new Date('2024-12-01'), isDeleted: false },
  { id: 'GB050', subjectId: 'SUBJ001', year: 2025, gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail', isActive: true, createdBy: 'system', dateCreated: new Date('2024-12-01'), isDeleted: false }
];

// Helper function to calculate grade based on score
const calculateGradeFromScore = (score: number): { grade: 'A' | 'B' | 'C' | 'D' | 'F', failResult: boolean } => {
  if (score >= 80) return { grade: 'A', failResult: false };
  if (score >= 70) return { grade: 'B', failResult: false };
  if (score >= 60) return { grade: 'C', failResult: false };
  if (score >= 50) return { grade: 'D', failResult: false };
  return { grade: 'F', failResult: true };
};

// Grade records based on the students from score entry system
const mockGrades: GradeDto[] = [
  // John Banda - STU001 - Mathematics Paper 1 - Score: 85 (Grade A)
  {
    id: 'GR001',
    studentId: 'STU001',
    subjectId: 'SUBJ001', // Mathematics
    paperId: 'PAP001', // Mathematics Paper 1
    year: 2024,
    startScore: 85,
    endScore: 85,
    assignedGrade: 'A',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2024-03-15'),
    isDeleted: false
  },
  // John Banda - STU001 - Mathematics Paper 2 - Score: 78 (Grade B)
  {
    id: 'GR002',
    studentId: 'STU001',
    subjectId: 'SUBJ001', // Mathematics
    paperId: 'PAP002', // Mathematics Paper 2
    year: 2024,
    startScore: 78,
    endScore: 78,
    assignedGrade: 'B',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2024-03-15'),
    isDeleted: false
  },
  // Peter Mwale - STU003 - Mathematics Paper 1 - Score: 72 (Grade B)
  {
    id: 'GR003',
    studentId: 'STU003',
    subjectId: 'SUBJ001', // Mathematics
    paperId: 'PAP001', // Mathematics Paper 1
    year: 2024,
    startScore: 72,
    endScore: 72,
    assignedGrade: 'B',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2024-03-15'),
    isDeleted: false
  },
  // Grace Tembo - STU004 - Mathematics Paper 1 - Score: 91 (Grade A)
  {
    id: 'GR004',
    studentId: 'STU004',
    subjectId: 'SUBJ001', // Mathematics
    paperId: 'PAP001', // Mathematics Paper 1
    year: 2024,
    startScore: 91,
    endScore: 91,
    assignedGrade: 'A',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2024-03-15'),
    isDeleted: false
  },
  // Sarah Banda - STU006 - English Language Paper 1 - Score: 78 (Grade B)
  {
    id: 'GR005',
    studentId: 'STU006',
    subjectId: 'SUBJ002', // English Language
    paperId: 'PAP003', // English Language Paper 1
    year: 2024,
    startScore: 78,
    endScore: 78,
    assignedGrade: 'B',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2024-03-15'),
    isDeleted: false
  },
  // Grace Mbewe - STU009 - English Language Paper 1 - Score: 82 (Grade A)
  {
    id: 'GR006',
    studentId: 'STU009',
    subjectId: 'SUBJ002', // English Language
    paperId: 'PAP003', // English Language Paper 1
    year: 2024,
    startScore: 82,
    endScore: 82,
    assignedGrade: 'A',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2024-03-15'),
    isDeleted: false
  },
  // Linda Mwale - STU008 - Physical Science Paper 1 - Score: 84 (Grade A)
  {
    id: 'GR007',
    studentId: 'STU008',
    subjectId: 'SUBJ003', // Physical Science
    paperId: 'PAP005', // Physical Science Paper 1
    year: 2024,
    startScore: 84,
    endScore: 84,
    assignedGrade: 'A',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2024-03-15'),
    isDeleted: false
  },
  // Additional historical grades for 2023
  {
    id: 'GR007',
    studentId: 'STU001',
    subjectId: 'SUBJ002', // English Language
    year: 2023,
    startScore: 76,
    endScore: 76,
    assignedGrade: 'B',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2023-11-15'),
    isDeleted: false
  },
  {
    id: 'GR008',
    studentId: 'STU003',
    subjectId: 'SUBJ003', // Physical Science
    year: 2023,
    startScore: 68,
    endScore: 68,
    assignedGrade: 'C',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2023-11-15'),
    isDeleted: false
  },
  {
    id: 'GR009',
    studentId: 'STU004',
    subjectId: 'SUBJ004', // Biology
    year: 2023,
    startScore: 89,
    endScore: 89,
    assignedGrade: 'A',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2023-11-15'),
    isDeleted: false
  },
  {
    id: 'GR010',
    studentId: 'STU006',
    subjectId: 'SUBJ001', // Mathematics
    year: 2023,
    startScore: 65,
    endScore: 65,
    assignedGrade: 'C',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2023-11-15'),
    isDeleted: false
  },
  // Some failing grades for realistic data
  {
    id: 'GR011',
    studentId: 'STU002', // Mary Phiri
    subjectId: 'SUBJ001', // Mathematics
    year: 2023,
    startScore: 45,
    endScore: 45,
    assignedGrade: 'F',
    failResult: true,
    createdBy: 'system',
    dateCreated: new Date('2023-11-15'),
    isDeleted: false
  },
  {
    id: 'GR012',
    studentId: 'STU005', // James Zulu
    subjectId: 'SUBJ002', // English Language
    year: 2023,
    startScore: 48,
    endScore: 48,
    assignedGrade: 'F',
    failResult: true,
    createdBy: 'system',
    dateCreated: new Date('2023-11-15'),
    isDeleted: false
  },
  // More diverse grades for different subjects
  {
    id: 'GR013',
    studentId: 'STU007', // Michael Phiri
    subjectId: 'SUBJ005', // Chemistry
    year: 2024,
    startScore: 73,
    endScore: 73,
    assignedGrade: 'B',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2024-03-15'),
    isDeleted: false
  },
  {
    id: 'GR014',
    studentId: 'STU010', // David Chirwa
    subjectId: 'SUBJ006', // Physics
    year: 2024,
    startScore: 67,
    endScore: 67,
    assignedGrade: 'C',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2024-03-15'),
    isDeleted: false
  },
  {
    id: 'GR015',
    studentId: 'STU008', // Linda Mwale
    subjectId: 'SUBJ007', // Geography
    year: 2024,
    startScore: 79,
    endScore: 79,
    assignedGrade: 'B',
    failResult: false,
    createdBy: 'system',
    dateCreated: new Date('2024-03-15'),
    isDeleted: false
  }
];

export class GradingService {
  // Grade CRUD Operations
  /**
   * Get all grades
   */
  async getAllGrades(): Promise<GradeDto[]> {
    try {
      // Mock implementation - return dummy data
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
      return [...mockGrades];
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch grades');
    }
  }

  /**
   * Get grade by ID
   */
  async getGradeById(id: string): Promise<GradeDto> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      const grade = mockGrades.find(g => g.id === id);
      if (!grade) throw new Error('Grade not found');
      return grade;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch grade');
    }
  }

  /**
   * Create new grade
   */
  async createGrade(gradeData: CreateGradeRequest): Promise<GradeDto> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      const newGrade: GradeDto = {
        id: `GR${String(mockGrades.length + 1).padStart(3, '0')}`,
        ...gradeData,
        createdBy: 'current-user',
        dateCreated: new Date(),
        isDeleted: false
      };
      mockGrades.push(newGrade);
      return newGrade;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create grade');
    }
  }

  /**
   * Update grade
   */
  async updateGrade(id: string, gradeData: UpdateGradeRequest): Promise<GradeDto> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      const index = mockGrades.findIndex(g => g.id === id);
      if (index === -1) throw new Error('Grade not found');

      mockGrades[index] = { ...mockGrades[index], ...gradeData };
      return mockGrades[index];
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update grade');
    }
  }

  /**
   * Delete grade
   */
  async deleteGrade(id: string): Promise<void> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      const index = mockGrades.findIndex(g => g.id === id);
      if (index === -1) throw new Error('Grade not found');

      mockGrades.splice(index, 1);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete grade');
    }
  }

  // Paper CRUD Operations
  /**
   * Get all papers
   */
  async getAllPapers(): Promise<PaperDto[]> {
    try {
      await new Promise(resolve => setTimeout(resolve, 400));
      return [...mockPapers];
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch papers');
    }
  }

  /**
   * Get papers by subject ID
   */
  async getPapersBySubject(subjectId: string): Promise<PaperDto[]> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      return mockPapers.filter(paper => paper.subjectId === subjectId);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch papers for subject');
    }
  }

  /**
   * Get paper by ID
   */
  async getPaperById(id: string): Promise<PaperDto> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      const paper = mockPapers.find(p => p.id === id);
      if (!paper) throw new Error('Paper not found');
      return paper;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch paper');
    }
  }

  /**
   * Create new paper
   */
  async createPaper(paperData: CreatePaperRequest): Promise<PaperDto> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      const newPaper: PaperDto = {
        id: `PAP${String(mockPapers.length + 1).padStart(3, '0')}`,
        ...paperData,
        createdBy: 'current-user',
        dateCreated: new Date(),
        isDeleted: false
      };
      mockPapers.push(newPaper);
      return newPaper;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create paper');
    }
  }

  /**
   * Update paper
   */
  async updatePaper(id: string, paperData: UpdatePaperRequest): Promise<PaperDto> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      const index = mockPapers.findIndex(p => p.id === id);
      if (index === -1) throw new Error('Paper not found');

      mockPapers[index] = { ...mockPapers[index], ...paperData };
      return mockPapers[index];
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update paper');
    }
  }

  /**
   * Delete paper
   */
  async deletePaper(id: string): Promise<void> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      const index = mockPapers.findIndex(p => p.id === id);
      if (index === -1) throw new Error('Paper not found');

      mockPapers.splice(index, 1);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete paper');
    }
  }

  // Subject CRUD Operations
  /**
   * Get all subjects
   */
  async getAllSubjects(): Promise<SubjectDto[]> {
    try {
      await new Promise(resolve => setTimeout(resolve, 400));
      return [...mockSubjects];
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch subjects');
    }
  }

  /**
   * Get subject by ID
   */
  async getSubjectById(id: string): Promise<SubjectDto> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      const subject = mockSubjects.find(s => s.id === id);
      if (!subject) throw new Error('Subject not found');
      return subject;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch subject');
    }
  }

  /**
   * Create new subject
   */
  async createSubject(subjectData: CreateSubjectRequest): Promise<SubjectDto> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      const newSubject: SubjectDto = {
        id: `SUBJ${String(mockSubjects.length + 1).padStart(3, '0')}`,
        ...subjectData,
        createdBy: 'current-user',
        dateCreated: new Date(),
        isDeleted: false
      };
      mockSubjects.push(newSubject);
      return newSubject;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create subject');
    }
  }

  /**
   * Update subject
   */
  async updateSubject(id: string, subjectData: UpdateSubjectRequest): Promise<SubjectDto> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      const index = mockSubjects.findIndex(s => s.id === id);
      if (index === -1) throw new Error('Subject not found');

      mockSubjects[index] = { ...mockSubjects[index], ...subjectData };
      return mockSubjects[index];
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update subject');
    }
  }

  /**
   * Delete subject
   */
  async deleteSubject(id: string): Promise<void> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      const index = mockSubjects.findIndex(s => s.id === id);
      if (index === -1) throw new Error('Subject not found');

      mockSubjects.splice(index, 1);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete subject');
    }
  }

  // Grade Boundary CRUD Operations
  /**
   * Get all grade boundaries
   */
  async getAllGradeBoundaries(): Promise<GradeBoundaryDto[]> {
    try {
      await new Promise(resolve => setTimeout(resolve, 400));
      return [...mockGradeBoundaries];
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch grade boundaries');
    }
  }

  /**
   * Get grade boundaries by subject ID
   */
  async getGradeBoundariesBySubject(subjectId: string): Promise<GradeBoundaryDto[]> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      return mockGradeBoundaries.filter(boundary => boundary.subjectId === subjectId);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch grade boundaries for subject');
    }
  }

  /**
   * Get grade boundaries by year
   */
  async getGradeBoundariesByYear(year: number): Promise<GradeBoundaryDto[]> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      return mockGradeBoundaries.filter(boundary => boundary.year === year);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch grade boundaries for year');
    }
  }

  /**
   * Get grade boundary by ID
   */
  async getGradeBoundaryById(id: string): Promise<GradeBoundaryDto> {
    try {
      return await apiClient.get<GradeBoundaryDto>(`/api/GradeBoundary/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch grade boundary');
    }
  }

  /**
   * Create new grade boundary
   */
  async createGradeBoundary(boundaryData: CreateGradeBoundaryRequest): Promise<GradeBoundaryDto> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      const newBoundary: GradeBoundaryDto = {
        id: `GB${String(mockGradeBoundaries.length + 1).padStart(3, '0')}`,
        ...boundaryData,
        createdBy: 'current-user',
        dateCreated: new Date(),
        isDeleted: false
      };
      mockGradeBoundaries.push(newBoundary);
      return newBoundary;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create grade boundary');
    }
  }

  /**
   * Update grade boundary
   */
  async updateGradeBoundary(id: string, boundaryData: UpdateGradeBoundaryRequest): Promise<GradeBoundaryDto> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      const index = mockGradeBoundaries.findIndex(b => b.id === id);
      if (index === -1) throw new Error('Grade boundary not found');

      mockGradeBoundaries[index] = { ...mockGradeBoundaries[index], ...boundaryData };
      return mockGradeBoundaries[index];
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update grade boundary');
    }
  }

  /**
   * Delete grade boundary
   */
  async deleteGradeBoundary(id: string): Promise<void> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      const index = mockGradeBoundaries.findIndex(b => b.id === id);
      if (index === -1) throw new Error('Grade boundary not found');

      mockGradeBoundaries.splice(index, 1);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete grade boundary');
    }
  }

  // Grade Configuration CRUD Operations
  /**
   * Get all grade configurations
   */
  async getAllGradeConfigurations(): Promise<GradeConfigurationDto[]> {
    try {
      return await apiClient.get<GradeConfigurationDto[]>('/api/GradeConfiguration');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch grade configurations');
    }
  }

  /**
   * Get grade configuration by ID
   */
  async getGradeConfigurationById(id: string): Promise<GradeConfigurationDto> {
    try {
      return await apiClient.get<GradeConfigurationDto>(`/api/GradeConfiguration/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch grade configuration');
    }
  }

  /**
   * Create new grade configuration
   */
  async createGradeConfiguration(configData: CreateGradeConfigurationRequest): Promise<GradeConfigurationDto> {
    try {
      return await apiClient.post<GradeConfigurationDto>('/api/GradeConfiguration', configData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create grade configuration');
    }
  }

  /**
   * Update grade configuration
   */
  async updateGradeConfiguration(id: string, configData: UpdateGradeConfigurationRequest): Promise<GradeConfigurationDto> {
    try {
      return await apiClient.put<GradeConfigurationDto>(`/api/GradeConfiguration/${id}`, configData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update grade configuration');
    }
  }

  /**
   * Delete grade configuration
   */
  async deleteGradeConfiguration(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/GradeConfiguration/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete grade configuration');
    }
  }

  // Utility Methods
  /**
   * Calculate grade based on score and subject boundaries
   */
  async calculateGrade(score: number, subjectId: string, year: number): Promise<string> {
    try {
      const response = await apiClient.post<{ grade: string }>('/api/Grade/calculate', {
        score,
        subjectId,
        year
      });
      return response.grade;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to calculate grade');
    }
  }

  /**
   * Bulk import grades
   */
  async bulkImportGrades(grades: CreateGradeRequest[]): Promise<GradeDto[]> {
    try {
      return await apiClient.post<GradeDto[]>('/api/Grade/bulk-import', grades);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to import grades');
    }
  }

  /**
   * Create comprehensive grade boundaries for a paper
   */
  async createComprehensiveGradeBoundaries(request: ComprehensiveGradeBoundaryRequest): Promise<BulkGradeBoundaryResponse> {
    try {
      await new Promise(resolve => setTimeout(resolve, 800)); // Simulate API delay

      const createdBoundaries: GradeBoundaryDto[] = [];
      const errors: string[] = [];

      // Validate that all grade levels are provided
      const requiredGrades: Array<'A' | 'B' | 'C' | 'D' | 'F'> = ['A', 'B', 'C', 'D', 'F'];
      const providedGrades = request.gradeBoundaries.map(gb => gb.gradeLevel);

      for (const grade of requiredGrades) {
        if (!providedGrades.includes(grade)) {
          errors.push(`Missing grade boundary for grade ${grade}`);
        }
      }

      // Validate score ranges don't overlap
      const sortedBoundaries = [...request.gradeBoundaries].sort((a, b) => b.minScore - a.minScore);
      for (let i = 0; i < sortedBoundaries.length - 1; i++) {
        const current = sortedBoundaries[i];
        const next = sortedBoundaries[i + 1];
        if (current.minScore <= next.maxScore) {
          errors.push(`Grade boundaries overlap between ${current.gradeLevel} and ${next.gradeLevel}`);
        }
      }

      if (errors.length > 0) {
        return { success: false, createdBoundaries: [], errors };
      }

      // Create boundaries
      for (const boundaryData of request.gradeBoundaries) {
        const newBoundary: GradeBoundaryDto = {
          id: `GB${String(mockGradeBoundaries.length + createdBoundaries.length + 1).padStart(3, '0')}`,
          examType: request.examType,
          subjectId: request.subjectId,
          paperId: request.paperId,
          year: request.year,
          gradeLevel: boundaryData.gradeLevel,
          minScore: boundaryData.minScore,
          maxScore: boundaryData.maxScore,
          description: boundaryData.description,
          isActive: request.isActive ?? true,
          createdBy: 'current-user',
          dateCreated: new Date(),
          isDeleted: false
        };

        mockGradeBoundaries.push(newBoundary);
        createdBoundaries.push(newBoundary);
      }

      return { success: true, createdBoundaries, errors: [] };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create comprehensive grade boundaries');
    }
  }

  /**
   * Get grade boundaries by exam type
   */
  async getGradeBoundariesByExamType(examType: ExamType): Promise<GradeBoundaryDto[]> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      return mockGradeBoundaries.filter(boundary => boundary.examType === examType);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch grade boundaries for exam type');
    }
  }

  /**
   * Get grade boundaries with filters
   */
  async getGradeBoundariesWithFilters(filters: {
    examType?: ExamType;
    subjectId?: string;
    paperId?: string;
    year?: number;
    isActive?: boolean;
  }): Promise<GradeBoundaryDto[]> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));

      let filtered = [...mockGradeBoundaries];

      if (filters.examType) {
        filtered = filtered.filter(b => b.examType === filters.examType);
      }
      if (filters.subjectId) {
        filtered = filtered.filter(b => b.subjectId === filters.subjectId);
      }
      if (filters.paperId) {
        filtered = filtered.filter(b => b.paperId === filters.paperId);
      }
      if (filters.year) {
        filtered = filtered.filter(b => b.year === filters.year);
      }
      if (filters.isActive !== undefined) {
        filtered = filtered.filter(b => b.isActive === filters.isActive);
      }

      return filtered;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch filtered grade boundaries');
    }
  }
}

// Create singleton instance
export const gradingService = new GradingService();
