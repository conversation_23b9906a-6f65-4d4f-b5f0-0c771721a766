<template>
  <div class="space-y-6">
    <!-- Header with Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">Grade Boundary Management</h2>
          <p class="text-sm text-gray-600 mt-1">Configure grade boundaries for subjects and academic years</p>
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="showComprehensiveForm = true"
            class="inline-flex items-center px-4 py-2 bg-maneb-primary text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2 transition-colors duration-200"
          >
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Setup Complete Boundaries
          </button>
          <button
            @click="showBoundaryModal = true"
            class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
          >
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Single Boundary
          </button>
        </div>
      </div>

      <!-- Filters -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Exam Type</label>
          <select
            v-model="selectedExamType"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
          >
            <option value="">All Exam Types</option>
            <option v-for="examType in gradingStore.availableExamTypes" :key="examType" :value="examType">
              {{ examType }}
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
          <select
            v-model="selectedSubject"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
          >
            <option value="">All Subjects</option>
            <option v-for="subject in gradingStore.activeSubjects" :key="subject.id" :value="subject.id">
              {{ subject.name }} ({{ subject.code }})
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Year</label>
          <select
            v-model="selectedYear"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
          >
            <option value="">All Years</option>
            <option v-for="year in availableYears" :key="year" :value="year">{{ year }}</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
          <select
            v-model="selectedStatus"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Grade Boundaries by Subject and Year -->
    <div v-if="gradingStore.isLoading" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-maneb-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Loading grade boundaries...
      </div>
    </div>

    <div v-else-if="filteredBoundaries.length === 0" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center text-gray-500">
      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
      </svg>
      <p class="text-lg font-medium text-gray-900 mb-2">No grade boundaries found</p>
      <p class="text-gray-600">Get started by adding your first grade boundary configuration.</p>
    </div>

    <div v-else class="space-y-6">
      <div v-for="group in groupedBoundaries" :key="`${group.subjectId}-${group.year}`" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-medium text-gray-900">{{ getSubjectName(group.subjectId) }}</h3>
              <p class="text-sm text-gray-600">{{ group.year }} Academic Year</p>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500">{{ group.boundaries.length }} boundaries</span>
              <button
                @click="addBoundaryToGroup(group.subjectId, group.year)"
                class="text-maneb-primary hover:text-red-700 text-sm font-medium transition-colors duration-200"
              >
                Add Boundary
              </button>
            </div>
          </div>
        </div>

        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div
              v-for="boundary in group.boundaries"
              :key="boundary.id"
              class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
              :class="getGradeBoundaryClass(boundary.gradeLevel)"
            >
              <div class="flex items-center justify-between mb-2">
                <div class="font-semibold text-lg">Grade {{ boundary.gradeLevel }}</div>
                <div class="flex items-center space-x-1">
                  <button
                    @click="editBoundary(boundary)"
                    class="p-1 text-gray-400 hover:text-maneb-primary transition-colors duration-200"
                    title="Edit Boundary"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  </button>
                  <button
                    @click="deleteBoundary(boundary)"
                    class="p-1 text-gray-400 hover:text-red-600 transition-colors duration-200"
                    title="Delete Boundary"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  </button>
                </div>
              </div>
              <div class="text-sm font-medium">{{ boundary.minScore }} - {{ boundary.maxScore }}%</div>
              <div v-if="boundary.description" class="text-xs text-gray-600 mt-1">{{ boundary.description }}</div>
              <div class="mt-2">
                <span
                  :class="boundary.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                >
                  {{ boundary.isActive ? 'Active' : 'Inactive' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Grade Boundary Modal -->
    <GradeBoundaryModal
      :is-open="showBoundaryModal"
      :boundary="selectedBoundary"
      :pre-selected-subject="preSelectedSubject"
      :pre-selected-year="preSelectedYear"
      @close="closeBoundaryModal"
      @success="handleBoundarySuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useGradingStore } from '@/store'
import type { GradeBoundaryDto } from '@/interfaces'
import GradeBoundaryModal from './GradeBoundaryModal.vue'
import ComprehensiveGradeBoundaryForm from './ComprehensiveGradeBoundaryForm.vue'
import sweetAlert from '@/utils/sweetAlert'

// Store
const gradingStore = useGradingStore()

// State
const showBoundaryModal = ref(false)
const showComprehensiveForm = ref(false)
const selectedBoundary = ref<GradeBoundaryDto | null>(null)
const selectedExamType = ref('')
const selectedSubject = ref('')
const selectedYear = ref('')
const selectedStatus = ref('')
const preSelectedSubject = ref('')
const preSelectedYear = ref<number | null>(null)

// Computed
const availableYears = computed(() => {
  const years = [...new Set(gradingStore.gradeBoundaries.map(b => b.year))].sort((a, b) => b - a)
  return years
})

const filteredBoundaries = computed(() => {
  let filtered = gradingStore.gradeBoundaries

  if (selectedSubject.value) {
    filtered = filtered.filter(boundary => boundary.subjectId === selectedSubject.value)
  }

  if (selectedYear.value) {
    filtered = filtered.filter(boundary => boundary.year === parseInt(selectedYear.value))
  }

  if (selectedStatus.value) {
    const isActive = selectedStatus.value === 'active'
    filtered = filtered.filter(boundary => boundary.isActive === isActive)
  }

  return filtered
})

const groupedBoundaries = computed(() => {
  const groups: { [key: string]: { subjectId: string; year: number; boundaries: GradeBoundaryDto[] } } = {}

  filteredBoundaries.value.forEach(boundary => {
    const key = `${boundary.subjectId}-${boundary.year}`
    if (!groups[key]) {
      groups[key] = {
        subjectId: boundary.subjectId,
        year: boundary.year,
        boundaries: []
      }
    }
    groups[key].boundaries.push(boundary)
  })

  // Sort boundaries within each group by grade level
  Object.values(groups).forEach(group => {
    group.boundaries.sort((a, b) => {
      const gradeOrder = { 'A': 5, 'B': 4, 'C': 3, 'D': 2, 'F': 1 }
      return gradeOrder[b.gradeLevel as keyof typeof gradeOrder] - gradeOrder[a.gradeLevel as keyof typeof gradeOrder]
    })
  })

  return Object.values(groups).sort((a, b) => {
    // Sort by year (descending) then by subject name
    if (a.year !== b.year) return b.year - a.year
    return getSubjectName(a.subjectId).localeCompare(getSubjectName(b.subjectId))
  })
})

// Methods
const getSubjectName = (subjectId: string): string => {
  const subject = gradingStore.subjects.find(s => s.id === subjectId)
  return subject?.name || 'Unknown Subject'
}

const getGradeBoundaryClass = (grade: string): string => {
  const classes = {
    'A': 'border-green-200 bg-green-50',
    'B': 'border-blue-200 bg-blue-50',
    'C': 'border-yellow-200 bg-yellow-50',
    'D': 'border-orange-200 bg-orange-50',
    'F': 'border-red-200 bg-red-50'
  }
  return classes[grade as keyof typeof classes] || 'border-gray-200 bg-gray-50'
}

const addBoundaryToGroup = (subjectId: string, year: number) => {
  preSelectedSubject.value = subjectId
  preSelectedYear.value = year
  selectedBoundary.value = null
  showBoundaryModal.value = true
}

const editBoundary = (boundary: GradeBoundaryDto) => {
  selectedBoundary.value = boundary
  preSelectedSubject.value = ''
  preSelectedYear.value = null
  showBoundaryModal.value = true
}

const deleteBoundary = async (boundary: GradeBoundaryDto) => {
  const result = await sweetAlert.confirm(
    'Delete Grade Boundary',
    `Are you sure you want to delete the Grade ${boundary.gradeLevel} boundary (${boundary.minScore}-${boundary.maxScore}%)? This action cannot be undone.`,
    'warning'
  )

  if (result.isConfirmed && boundary.id) {
    try {
      await gradingStore.deleteGradeBoundary(boundary.id)
      await sweetAlert.toast.success('Grade boundary deleted successfully')
    } catch (error) {
      await sweetAlert.error('Error', 'Failed to delete grade boundary')
    }
  }
}

const closeBoundaryModal = () => {
  showBoundaryModal.value = false
  selectedBoundary.value = null
  preSelectedSubject.value = ''
  preSelectedYear.value = null
}

const handleBoundarySuccess = () => {
  closeBoundaryModal()
  // Refresh boundaries list
  gradingStore.fetchGradeBoundaries()
}

// Lifecycle
onMounted(() => {
  if (gradingStore.gradeBoundaries.length === 0) {
    gradingStore.fetchGradeBoundaries()
  }
  if (gradingStore.subjects.length === 0) {
    gradingStore.fetchSubjects()
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.hover\:text-maneb-primary:hover {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
