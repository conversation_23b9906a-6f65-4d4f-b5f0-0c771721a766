import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { authService } from '@/services';
import type { LoginModel, UserDto, AuthResponse } from '@/interfaces';

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<UserDto | null>(null);
  const token = ref<string | null>(null);
  const refreshToken = ref<string | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value);
  const userFullName = computed(() => user.value?.fullName || '');
  const userStatus = computed(() => user.value?.status || 0);
  const isApproved = computed(() => user.value?.status === 1 || user.value?.status === 2);

  // Actions
  const login = async (credentials: LoginModel): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;

      const response: AuthResponse = await authService.login(credentials);

      console.log('Auth service response:', response);
      console.log('Response user:', response.user);
      console.log('Response token:', response.token);

      // Update store state
      user.value = response.user;
      token.value = response.token;
      refreshToken.value = response.refreshToken;

      console.log('Auth store after update:');
      console.log('- user.value:', user.value);
      console.log('- token.value:', token.value);
      console.log('- isAuthenticated:', isAuthenticated.value);
      
    } catch (err: any) {
      error.value = err.message || 'Login failed';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      isLoading.value = true;
      await authService.logout();
    } catch (err: any) {
      console.warn('Logout error:', err);
    } finally {
      // Clear store state regardless of API call success
      user.value = null;
      token.value = null;
      refreshToken.value = null;
      error.value = null;
      isLoading.value = false;
    }
  };

  const refreshAuthToken = async (): Promise<void> => {
    try {
      const response: AuthResponse = await authService.refreshToken();
      
      // Update store state
      user.value = response.user;
      token.value = response.token;
      refreshToken.value = response.refreshToken;
      
    } catch (err: any) {
      // If refresh fails, logout user
      await logout();
      throw err;
    }
  };

  const initializeAuth = (): void => {
    // Load auth data from localStorage on app initialization
    const storedUser = authService.getCurrentUser();
    const storedToken = authService.getToken();
    const storedRefreshToken = localStorage.getItem('refresh_token');

    if (storedUser && storedToken && !authService.isTokenExpired()) {
      user.value = storedUser;
      token.value = storedToken;
      refreshToken.value = storedRefreshToken;
    } else if (storedRefreshToken) {
      // Try to refresh token if main token is expired
      refreshAuthToken().catch(() => {
        // If refresh fails, clear everything
        logout();
      });
    }
  };

  const clearError = (): void => {
    error.value = null;
  };

  const updateUser = (updatedUser: UserDto): void => {
    user.value = updatedUser;
    localStorage.setItem('user', JSON.stringify(updatedUser));
  };

  // Check if user has specific status
  const hasStatus = (status: string): boolean => {
    return user.value?.status === status;
  };

  // Check if user can perform admin actions
  const canPerformAdminActions = computed(() => {
    return user.value?.status === 'SecondApproved';
  });

  // Check if user is an admin (Status = 2)
  const isAdmin = computed(() => {
    return false;
    //return user.value?.status === 2;
  });

  // Check if user is a regular student/user (Status = 1)
  const isStudent = computed(() => {
    return user.value?.status === 1;
  });

  // Get user role as string
  const userRole = computed(() => {
    if (user.value?.status === 2) return 'admin';
    if (user.value?.status === 1) return 'student';
    return 'unknown';
  });

  // Get appropriate dashboard route based on user role
  const getDashboardRoute = computed(() => {
    if (isAdmin.value) return '/admin/dashboard';
    if (isStudent.value) return '/student/dashboard';
    return '/student/dashboard'; // Default to student dashboard
  });

  return {
    // State
    user,
    token,
    refreshToken,
    isLoading,
    error,
    
    // Getters
    isAuthenticated,
    userFullName,
    userStatus,
    isApproved,
    canPerformAdminActions,
    isAdmin,
    isStudent,
    userRole,
    getDashboardRoute,
    
    // Actions
    login,
    logout,
    refreshAuthToken,
    initializeAuth,
    clearError,
    updateUser,
    hasStatus,
  };
});
